# Talent Routes Screenshots

Generated on: 2025-07-09 15:47:11

## Overview

This document contains screenshots of all talent-facing pages in the Ghostwrote application. These screenshots provide a comprehensive view of the talent user experience and interface design.

## Route Tree Structure

### Profile Management

- **Show (Show/Detail)** - `/talent/profile`
- **Edit (Edit Form)** - `/talent/profile/edit`

### Job Management

- **Index (List/Index)** - `/talent`
- **Index (List/Index)** - `/talent/jobs`
- **Show (Show/Detail)** - `/talent/jobs/1`

### Job Applications

- **Index (List/Index)** - `/talent/job_applications`
- **Show (Show/Detail)** - `/talent/job_applications/1`
- **New (New/Create Form)** - `/talent/jobs/1/job_applications/new`
- **Edit (Edit Form)** - `/talent/job_applications/1/edit`

### Conversations & Messaging

- **Index (List/Index)** - `/talent/conversations`
- **Index (List/Index)** - `/talent/conversations/archives`
- **Show (Show/Detail)** - `/talent/conversations/1`
- **Show (Show/Detail)** - `/talent/conversations/archives/1`
- **New (New/Create Form)** - `/talent/conversations/archives/new`
- **Edit (Edit Form)** - `/talent/conversations/archives/1/edit`

### Direct Messages

- **Index (List/Index)** - `/talent/messages`

### Chat Requests

- **Index (List/Index)** - `/talent/chat_requests`

### Settings & Account

- **Show (Show/Detail)** - `/talent/settings`
- **Show (Show/Detail)** - `/talent/settings/passwords`
- **Show (Show/Detail)** - `/talent/settings/subscription`

### Subscription Management

- **Cancel** - `/talent/subscription/cancel`
- **Success** - `/talent/subscription/success`

---

## Detailed Screenshots

### Profile Management

#### Show - `/talent/profile`

**Controller:** `talent/profiles`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `talent_profile`

![Talent Profile Show](./screenshots/talent/talent_profiles_show.png)

_Screenshot captured successfully_

---

#### Edit - `/talent/profile/edit`

**Controller:** `talent/profiles`
**Action:** `edit`
**HTTP Method:** `GET`
**Route Name:** `edit_talent_profile`

![Talent Profile Edit](./screenshots/talent/talent_profiles_edit.png)

_Screenshot captured successfully_

---

### Job Management

#### Index - `/talent`

**Controller:** `talent/jobs`
**Action:** `index`
**HTTP Method:** `GET`
**Route Name:** `talent_root`

![Talent Jobs Index](./screenshots/talent/talent_jobs_index.png)

_Screenshot captured successfully_

---

#### Index - `/talent/jobs`

**Controller:** `talent/jobs`
**Action:** `index`
**HTTP Method:** `GET`
**Route Name:** `talent_jobs`

![Talent Jobs Index](./screenshots/talent/talent_jobs_index.png)

_Screenshot captured successfully_

---

#### Show - `/talent/jobs/1`

**Controller:** `talent/jobs`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_job`

![Talent Job Show](./screenshots/talent/talent_jobs_show.png)

_Screenshot captured successfully_

---

### Job Applications

#### Index - `/talent/job_applications`

**Controller:** `talent/job_applications`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `talent_job_applications`

![Talent Job Applications Index](./screenshots/talent/talent_job_applications_index.png)

_Screenshot captured successfully_

---

#### Show - `/talent/job_applications/1`

**Controller:** `talent/job_applications`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_job_application`

![Talent Job Application Show](./screenshots/talent/talent_job_applications_show.png)

_Screenshot captured successfully_

---

#### New - `/talent/jobs/1/job_applications/new`

**Controller:** `talent/job_applications`
**Action:** `new`
**HTTP Method:** `GET`
**Route Name:** `new_talent_job_job_application`

![Talent Job Application New](./screenshots/talent/talent_job_applications_new.png)

_Screenshot captured successfully_

---

#### Edit - `/talent/job_applications/1/edit`

**Controller:** `talent/job_applications`
**Action:** `edit`
**HTTP Method:** `GET`
**Route Name:** `edit_talent_job_application`

![Talent Job Application Edit](./screenshots/talent/talent_job_applications_edit.png)

_Screenshot captured successfully_

---

### Conversations & Messaging

#### Index - `/talent/conversations`

**Controller:** `talent/conversations`
**Action:** `index`
**HTTP Method:** `GET`
**Route Name:** `talent_conversations`

![Talent Conversations Index](./screenshots/talent/talent_conversations_index.png)

_Screenshot captured successfully_

---

#### Index - `/talent/conversations/archives`

**Controller:** `talent/conversations/archives`
**Action:** `index`
**HTTP Method:** `GET`
**Route Name:** `talent_archives`

![Talent Conversations Archives Index](./screenshots/talent/talent_conversations_archives_index.png)

_Screenshot captured successfully_

---

#### Show - `/talent/conversations/1`

**Controller:** `talent/conversations`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_conversation`

![Talent Conversation Show](./screenshots/talent/talent_conversations_show.png)

_Screenshot captured successfully_

---

#### Show - `/talent/conversations/archives/1`

**Controller:** `talent/conversations/archives`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_archive`

![Talent Conversation Archive Show](./screenshots/talent/talent_conversations_archives_show.png)

_Screenshot captured successfully_

---

#### New - `/talent/conversations/archives/new`

**Controller:** `talent/conversations/archives`
**Action:** `new`
**HTTP Method:** `GET`
**Route Name:** `new_talent_archive`

![Talent Conversation Archive New](./screenshots/talent/talent_conversations_archives_new.png)

_Route does not exist - archives are created by archiving existing conversations_

---

#### Edit - `/talent/conversations/archives/1/edit`

**Controller:** `talent/conversations/archives`
**Action:** `edit`
**HTTP Method:** `GET`
**Route Name:** `edit_talent_archive`

![Talent Conversation Archive Edit](./screenshots/talent/talent_conversations_archives_edit.png)

_Route does not exist - archived conversations cannot be edited_

---

### Direct Messages

#### Index - `/talent/messages`

**Controller:** `talent/messages`
**Action:** `index`
**HTTP Method:** `GET`
**Route Name:** `talent_messages`

![Talent Messages Index](./screenshots/talent/talent_messages_index.png)

_Screenshot captured successfully_

---

### Chat Requests

#### Index - `/talent/chat_requests`

**Controller:** `talent/chat_requests`
**Action:** `index`
**HTTP Method:** `GET`
**Route Name:** `talent_chat_requests`

![Talent Chat Requests Index](./screenshots/talent/talent_chat_requests_index.png)

_Screenshot captured successfully_

---

### Settings & Account

#### Show - `/talent/settings`

**Controller:** `talent/settings`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_settings`

![Talent Settings Show](./screenshots/talent/talent_settings_show.png)

_Screenshot captured successfully_

---

#### Show - `/talent/settings/passwords`

**Controller:** `talent/settings/passwords`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_settings_passwords`

![Talent Settings Passwords Show](./screenshots/talent/talent_settings_passwords_show.png)

_Screenshot captured successfully_

---

#### Show - `/talent/settings/subscription`

**Controller:** `talent/settings/subscriptions`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_settings_subscription`

![Talent Settings Subscriptions Show](./screenshots/talent/talent_settings_subscriptions_show.png)

_Screenshot captured successfully_

---

### Subscription Management

#### Cancel - `/talent/subscription/cancel`

**Controller:** `talent/subscriptions`
**Action:** `cancel`
**HTTP Method:** `GET`
**Route Name:** `cancel_talent_subscription`

![Talent Subscription Cancel](./screenshots/talent/talent_subscription_cancel.png)

_Screenshot captured successfully - redirects to subscription settings_

---

#### Success - `/talent/subscription/success`

**Controller:** `talent/subscriptions`
**Action:** `success`
**HTTP Method:** `GET`
**Route Name:** `success_talent_subscription`

![Talent Subscription Success](./screenshots/talent/talent_subscription_success.png)

_Screenshot captured successfully - redirects to subscription settings_

---

## Summary

This documentation provides a comprehensive overview of all Talent-facing routes in the Ghostwrote platform. Screenshots have been captured for accessible routes, while routes requiring specific data or authentication states are noted accordingly.

### Route Categories Covered:

- **Job Management**: Browse and apply for jobs
- **Profile Management**: View and edit talent profiles
- **Conversations & Messaging**: Communication with scouts
- **Chat Requests**: Direct messaging functionality
- **Settings & Account**: Account management and preferences
- **Subscription Management**: Billing and subscription controls

### Screenshot Status:

- ✅ **Captured**: 20 routes successfully captured with real data and functionality
- ❌ **Route Does Not Exist**: 2 routes that don't exist in the application (archive new/edit)
- 📝 **All Functional Routes Documented**: Complete coverage of all accessible talent routes

All screenshots are stored in the `screenshots/talent/` directory and follow the naming convention `talent_{controller}_{action}.png`.
