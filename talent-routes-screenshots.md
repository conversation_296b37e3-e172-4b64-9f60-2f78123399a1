# Talent Routes Screenshots

Generated on: 2025-07-09 15:47:11

## Overview

This document contains screenshots of all talent-facing pages in the Ghostwrote application. These screenshots provide a comprehensive view of the talent user experience and interface design.

## Route Tree Structure

### Profile Management

- **Show (Show/Detail)** - `/talent/profile`
- **Edit (Edit Form)** - `/talent/profile/edit`

### Job Management

- **Index (List/Index)** - `/talent`
- **Index (List/Index)** - `/talent/jobs`
- **Show (Show/Detail)** - `/talent/jobs/1`

### Job Applications

- **Index (List/Index)** - `/talent/job_applications`
- **Show (Show/Detail)** - `/talent/job_applications/1`
- **New (New/Create Form)** - `/talent/jobs/1/job_applications/new`
- **Edit (Edit Form)** - `/talent/job_applications/1/edit`

### Conversations & Messaging

- **Index (List/Index)** - `/talent/conversations`
- **Index (List/Index)** - `/talent/conversations/archives`
- **Show (Show/Detail)** - `/talent/conversations/1`
- **Show (Show/Detail)** - `/talent/conversations/archives/1`
- **New (New/Create Form)** - `/talent/conversations/archives/new`
- **Edit (Edit Form)** - `/talent/conversations/archives/1/edit`

### Direct Messages

- **Index (List/Index)** - `/talent/messages`

### Chat Requests

- **Index (List/Index)** - `/talent/chat_requests`

### Settings & Account

- **Show (Show/Detail)** - `/talent/settings`
- **Show (Show/Detail)** - `/talent/settings/passwords`
- **Show (Show/Detail)** - `/talent/settings/subscription`

### Subscription Management

- **Cancel** - `/talent/subscription/cancel`
- **Success** - `/talent/subscription/success`

---

## Detailed Screenshots

### Profile Management

#### Show - `/talent/profile`

**Controller:** `talent/profiles`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `talent_profile`

![talent/profiles_show](screenshots/talent/talent_profiles_show.png)

_Screenshot captured successfully_

---

#### Edit - `/talent/profile/edit`

**Controller:** `talent/profiles`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_talent_profile`

![talent/profiles_edit](screenshots/talent/talent_profiles_edit.png)

_Screenshot captured successfully_

---

### Job Management

#### Index - `/talent`

**Controller:** `talent/jobs`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `talent_root`

![talent/jobs_index](screenshots/talent/talent_jobs_index.png)

_Screenshot captured successfully_

---

#### Index - `/talent/jobs`

**Controller:** `talent/jobs`
**Action:** `index`
**HTTP Method:** `GET`
**Route Name:** `talent_jobs`

![talent/jobs_index](screenshots/talent/talent_jobs_index.png)

_Screenshot captured successfully_

---

#### Show - `/talent/jobs/1`

**Controller:** `talent/jobs`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_job`

![talent/jobs_show](screenshots/talent/talent_jobs_show.png)

_Screenshot captured successfully_

---

### Job Applications

#### Index - `/talent/job_applications`

**Controller:** `talent/job_applications`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `talent_job_applications`

![talent/job_applications_index](screenshots/talent/talent_job_applications_index.png)

_Screenshot captured successfully_

---

#### Show - `/talent/job_applications/1`

**Controller:** `talent/job_applications`
**Action:** `show`
**HTTP Method:** `GET`
**Route Name:** `talent_job_application`

![talent/job_applications_show](screenshots/talent/talent_job_applications_show.png)

_Route not accessible - requires existing job application_

---

#### New - `/talent/jobs/1/job_applications/new`

**Controller:** `talent/job_applications`
**Action:** `new`
**HTTP Method:** `GET`
**Route Name:** `new_talent_job_job_application`

![talent/job_applications_new](screenshots/talent/talent_job_applications_new.png)

_Screenshot captured successfully_

---

#### Edit - `/talent/job_applications/1/edit`

**Controller:** `talent/job_applications`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_talent_job_application`

![talent/job_applications_edit](screenshots/talent/talent_job_applications_edit.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Conversations & Messaging

#### Index - `/talent/conversations`

**Controller:** `talent/conversations`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `talent_conversations`

![talent/conversations_index](screenshots/talent/talent_conversations_index.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Index - `/talent/conversations/archives`

**Controller:** `talent/conversations/archives`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `talent_archives`

![talent/conversations/archives_index](screenshots/talent/talent_conversations_archives_index.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show - `/talent/conversations/1`

**Controller:** `talent/conversations`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `talent_conversation`

![talent/conversations_show](screenshots/talent/talent_conversations_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show - `/talent/conversations/archives/1`

**Controller:** `talent/conversations/archives`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `talent_archive`

![talent/conversations/archives_show](screenshots/talent/talent_conversations_archives_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### New - `/talent/conversations/archives/new`

**Controller:** `talent/conversations/archives`  
**Action:** `new`  
**HTTP Method:** `GET`  
**Route Name:** `new_talent_archive`

![talent/conversations/archives_new](screenshots/talent/talent_conversations_archives_new.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Edit - `/talent/conversations/archives/1/edit`

**Controller:** `talent/conversations/archives`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_talent_archive`

![talent/conversations/archives_edit](screenshots/talent/talent_conversations_archives_edit.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Direct Messages

#### Index - `/talent/messages`

**Controller:** `talent/messages`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `talent_messages`

![talent/messages_index](screenshots/talent/talent_messages_index.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Chat Requests

#### Index - `/talent/chat_requests`

**Controller:** `talent/chat_requests`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `talent_chat_requests`

![talent/chat_requests_index](screenshots/talent/talent_chat_requests_index.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Settings & Account

#### Show - `/talent/settings`

**Controller:** `talent/settings`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `talent_settings`

![talent/settings_show](screenshots/talent/talent_settings_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show - `/talent/settings/passwords`

**Controller:** `talent/settings/passwords`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `talent_settings_passwords`

![talent/settings/passwords_show](screenshots/talent/talent_settings_passwords_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show - `/talent/settings/subscription`

**Controller:** `talent/settings/subscriptions`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `talent_settings_subscription`

![talent/settings/subscriptions_show](screenshots/talent/talent_settings_subscriptions_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Subscription Management

#### Cancel - `/talent/subscription/cancel`

**Controller:** `talent/subscriptions`  
**Action:** `cancel`  
**HTTP Method:** `GET`  
**Route Name:** `cancel_talent_subscription`

![talent/subscriptions_cancel](screenshots/talent/talent_subscriptions_cancel.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Success - `/talent/subscription/success`

**Controller:** `talent/subscriptions`  
**Action:** `success`  
**HTTP Method:** `GET`  
**Route Name:** `success_talent_subscription`

![talent/subscriptions_success](screenshots/talent/talent_subscriptions_success.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---
