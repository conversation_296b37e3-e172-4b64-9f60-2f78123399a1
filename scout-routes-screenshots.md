# Scout Routes Screenshots

Generated on: 2025-07-09 15:47:11
Updated with screenshots on: 2025-07-09 16:18:00

## Overview

This document contains screenshots of all scout-facing pages in the Ghostwrote application. These screenshots provide a comprehensive view of the scout user experience and interface design.

### Screenshot Status

✅ **Captured Screenshots:**

- Scout Dashboard (`/scout`)
- Jobs Index (`/scout/jobs`)
- Job Show (`/scout/jobs/:id`)
- Job Edit (`/scout/jobs/:id/edit`)
- <PERSON> New (`/scout/jobs/new`)
- Job Applicants (`/scout/jobs/:id/applicants`)
- Talent Index (`/scout/talent`)
- Applicants Index (`/scout/applicants`)
- Applicant Show (sidebar view)
- Conversations Index (`/scout/conversations`)
- Conversation Show (`/scout/conversations/:id`)

⏳ **Remaining Screenshots:**

- Additional routes listed below with placeholder status

## Route Tree Structure

### Job Management

- **Index (List/Index)** - `/scout`
- **Index (List/Index)** - `/scout/jobs`
- **Index (List/Index)** - `/scout/jobs/1/applicants`
- **Show (Show/Detail)** - `/scout/jobs/1`
- **New (New/Create Form)** - `/scout/jobs/new`
- **Edit (Edit Form)** - `/scout/jobs/1/edit`
- **Duplicate** - `/scout/jobs/1/duplicate`
- **Preview** - `/scout/jobs/1/preview`

### Talent Discovery

- **Index (List/Index)** - `/scout/talent`
- **Show (Show/Detail)** - `/scout/talent/1`

### Applicant Management

- **Index (List/Index)** - `/scout/applicants`
- **Show (Show/Detail)** - `/scout/applicants/1`
- **Details** - `/scout/applicants/1/details`
- **Stage Change Form** - `/scout/applicants/1/stage_change_form`
- **Placeholder** - `/scout/applicants/placeholder`

### Conversations & Messaging

- **Index (List/Index)** - `/scout/conversations`
- **Index (List/Index)** - `/scout/conversations/archives`
- **Show (Show/Detail)** - `/scout/conversations/1`
- **Show (Show/Detail)** - `/scout/conversations/archives/1`
- **Show Modal** - `/scout/conversations/modal/:applicant_user_id`

### Direct Messages

- **Index (List/Index)** - `/scout/messages`

### Chat Requests

- **New (New/Create Form)** - `/scout/chat_requests/new`

### Invitations

- **New (New/Create Form)** - `/scout/invitations/new`

### Settings & Account

- **Show (Show/Detail)** - `/scout/settings`
- **Show (Show/Detail)** - `/scout/settings/account`
- **Show (Show/Detail)** - `/scout/settings/organization`
- **Show (Show/Detail)** - `/scout/settings/subscription`
- **Edit (Edit Form)** - `/scout/settings/edit`

### Subscription Management

- **Cancel** - `/scout/subscription/cancel`
- **Success** - `/scout/subscription/success`

### Scout / Job Payments

- **Cancel** - `/scout/jobs/1/payment/cancel`
- **Success** - `/scout/jobs/1/payment/success`

### Scout / Applicant Notes

- **New (New/Create Form)** - `/scout/applicants/:applicant_id/notes/new`

---

## Detailed Screenshots

### Job Management

#### Index - `/scout`

**Controller:** `scout/jobs`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `scout_root`

![scout/jobs_index](screenshots/scout/scout_jobs_index.png)

_Screenshot captured - Shows the main scout dashboard with job listings, status filters, and management options_

---

#### Index - `/scout/jobs`

**Controller:** `scout/jobs`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `scout_jobs`

![scout/jobs_index](screenshots/scout/scout_jobs_index.png)

_Screenshot captured - Shows the jobs index page with job listings, status filters, and management options_

---

#### Index - `/scout/jobs/1/applicants`

**Controller:** `scout/jobs/applicants`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `scout_job_applicants`

![scout/jobs/applicants_index](screenshots/scout/scout_job_applicants.png)

_Screenshot captured - Shows job-specific applicants page with application stage filters and candidate management_

---

#### Show - `/scout/jobs/1`

**Controller:** `scout/jobs`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_job`

![scout/jobs_show](screenshots/scout/scout_jobs_show.png)

_Screenshot captured - Shows detailed job view with all job information and management options_

---

#### New - `/scout/jobs/new`

**Controller:** `scout/jobs`  
**Action:** `new`  
**HTTP Method:** `GET`  
**Route Name:** `new_scout_job`

![scout/jobs_new](screenshots/scout/scout_jobs_new.png)

_Screenshot captured - Shows multi-step job creation form with category selection and progress stepper_

---

#### Edit - `/scout/jobs/1/edit`

**Controller:** `scout/jobs`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_scout_job`

![scout/jobs_edit](screenshots/scout/scout_jobs_edit.png)

_Screenshot captured - Shows job edit form with all job details and update options_

---

#### Duplicate - `/scout/jobs/1/duplicate`

**Controller:** `scout/jobs`  
**Action:** `duplicate`  
**HTTP Method:** `GET`  
**Route Name:** `duplicate_scout_job`

![scout/jobs_duplicate](screenshots/scout/scout_jobs_duplicate.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Preview - `/scout/jobs/1/preview`

**Controller:** `scout/jobs`  
**Action:** `preview`  
**HTTP Method:** `GET`  
**Route Name:** `preview_scout_job`

![scout/jobs_preview](screenshots/scout/scout_jobs_preview.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Talent Discovery

#### Index - `/scout/talent`

**Controller:** `scout/talent`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `scout_talent_index`

![scout/talent_index](screenshots/scout/scout_talent_index.png)

_Screenshot captured - Shows talent search and discovery page with filtering options_

---

#### Show - `/scout/talent/1`

**Controller:** `scout/talent`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_talent`

![scout/talent_show](screenshots/scout/scout_talent_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Applicant Management

#### Index - `/scout/applicants`

**Controller:** `scout/applicants`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `scout_applicants`

![scout/applicants_index](screenshots/scout/scout_applicants_index.png)

_Screenshot captured - Shows all applicants across jobs with filtering and management tools_

---

#### Show - `/scout/applicants/1`

**Controller:** `scout/applicants`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_applicant`

![scout/applicants_show](screenshots/scout/scout_applicants_show.png)

_Screenshot captured - Shows detailed applicant view with sidebar showing candidate information and actions_

---

#### Details - `/scout/applicants/1/details`

**Controller:** `scout/applicants`  
**Action:** `details`  
**HTTP Method:** `GET`  
**Route Name:** `details_scout_applicant`

![scout/applicants_details](screenshots/scout/scout_applicants_details.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Stage Change Form - `/scout/applicants/1/stage_change_form`

**Controller:** `scout/applicants`  
**Action:** `stage_change_form`  
**HTTP Method:** `GET`  
**Route Name:** `stage_change_form_scout_applicant`

![scout/applicants_stage_change_form](screenshots/scout/scout_applicants_stage_change_form.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Placeholder - `/scout/applicants/placeholder`

**Controller:** `scout/applicants`  
**Action:** `placeholder`  
**HTTP Method:** `GET`  
**Route Name:** `placeholder_scout_applicants`

![scout/applicants_placeholder](screenshots/scout/scout_applicants_placeholder.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Conversations & Messaging

#### Index - `/scout/conversations`

**Controller:** `scout/conversations`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `scout_conversations`

![scout/conversations_index](screenshots/scout/scout_conversations_index.png)

_Screenshot captured - Shows conversations list with message previews and filtering options_

---

#### Index - `/scout/conversations/archives`

**Controller:** `scout/conversations/archives`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `scout_archives`

![scout/conversations/archives_index](screenshots/scout/scout_conversations_archives_index.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show - `/scout/conversations/1`

**Controller:** `scout/conversations`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_conversation`

![scout/conversations_show](screenshots/scout/scout_conversations_show.png)

_Screenshot captured - Shows individual conversation thread with messaging interface and job context_

---

#### Show - `/scout/conversations/archives/1`

**Controller:** `scout/conversations/archives`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_archive`

![scout/conversations/archives_show](screenshots/scout/scout_conversations_archives_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show Modal - `/scout/conversations/modal/:applicant_user_id`

**Controller:** `scout/conversations`  
**Action:** `show_modal`  
**HTTP Method:** `GET`  
**Route Name:** `modal_scout_conversations`

![scout/conversations_show_modal](screenshots/scout/scout_conversations_show_modal.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Direct Messages

#### Index - `/scout/messages`

**Controller:** `scout/messages`  
**Action:** `index`  
**HTTP Method:** `GET`  
**Route Name:** `scout_messages`

![scout/messages_index](screenshots/scout/scout_messages_index.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Chat Requests

#### New - `/scout/chat_requests/new`

**Controller:** `scout/chat_requests`  
**Action:** `new`  
**HTTP Method:** `GET`  
**Route Name:** `scout_new_chat_request`

![scout/chat_requests_new](screenshots/scout/scout_chat_requests_new.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Invitations

#### New - `/scout/invitations/new`

**Controller:** `scout/invitations`  
**Action:** `new`  
**HTTP Method:** `GET`  
**Route Name:** `new_scout_invitation`

![scout/invitations_new](screenshots/scout/scout_invitations_new.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Settings & Account

#### Show - `/scout/settings`

**Controller:** `scout/settings`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_settings`

![scout/settings_show](screenshots/scout/scout_settings_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show - `/scout/settings/account`

**Controller:** `scout/settings/accounts`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_settings_account`

![scout/settings/accounts_show](screenshots/scout/scout_settings_accounts_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show - `/scout/settings/organization`

**Controller:** `scout/settings/organizations`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_settings_organization`

![scout/settings/organizations_show](screenshots/scout/scout_settings_organizations_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Show - `/scout/settings/subscription`

**Controller:** `scout/settings/subscriptions`  
**Action:** `show`  
**HTTP Method:** `GET`  
**Route Name:** `scout_settings_subscription`

![scout/settings/subscriptions_show](screenshots/scout/scout_settings_subscriptions_show.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Edit - `/scout/settings/edit`

**Controller:** `scout/settings`  
**Action:** `edit`  
**HTTP Method:** `GET`  
**Route Name:** `edit_scout_settings`

![scout/settings_edit](screenshots/scout/scout_settings_edit.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Subscription Management

#### Cancel - `/scout/subscription/cancel`

**Controller:** `scout/subscriptions`  
**Action:** `cancel`  
**HTTP Method:** `GET`  
**Route Name:** `cancel_scout_subscription`

![scout/subscriptions_cancel](screenshots/scout/scout_subscriptions_cancel.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Success - `/scout/subscription/success`

**Controller:** `scout/subscriptions`  
**Action:** `success`  
**HTTP Method:** `GET`  
**Route Name:** `success_scout_subscription`

![scout/subscriptions_success](screenshots/scout/scout_subscriptions_success.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Scout / Job Payments

#### Cancel - `/scout/jobs/1/payment/cancel`

**Controller:** `scout/job_payments`  
**Action:** `cancel`  
**HTTP Method:** `GET`  
**Route Name:** `cancel_scout_job_payment`

![scout/job_payments_cancel](screenshots/scout/scout_job_payments_cancel.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

#### Success - `/scout/jobs/1/payment/success`

**Controller:** `scout/job_payments`  
**Action:** `success`  
**HTTP Method:** `GET`  
**Route Name:** `success_scout_job_payment`

![scout/job_payments_success](screenshots/scout/scout_job_payments_success.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---

### Scout / Applicant Notes

#### New - `/scout/applicants/:applicant_id/notes/new`

**Controller:** `scout/applicant_notes`  
**Action:** `new`  
**HTTP Method:** `GET`  
**Route Name:** `new_scout_applicant_note`

![scout/applicant_notes_new](screenshots/scout/scout_applicant_notes_new.png)

_Screenshot placeholder - Image will be captured during screenshot automation process_

---
